/**
 * 资质管理 Composable
 * 负责资质配置的获取、处理和状态管理
 */
import { ref, computed } from 'vue'
import { getQueryQualificationConfigget } from '~/services/edith_get_query_qualification_configget'
import type { QualificationManagementState, QualificationItemData } from './types'

export function useQualificationManagement() {
  // 响应式状态
  const state = ref<QualificationManagementState>({
    loading: false,
    error: null,
    qualificationList: [],
    qualificationData: {}
  })

  // 计算属性
  const loading = computed(() => state.value.loading)
  const error = computed(() => state.value.error)
  const qualificationList = computed(() => state.value.qualificationList)
  const qualificationData = computed(() => state.value.qualificationData)
  const hasQualifications = computed(() => state.value.qualificationList.length > 0)

  // 加载资质类型配置
  const loadQualificationTypes = async (categoryId: string) => {
    if (!categoryId) {
      state.value.qualificationList = []
      return
    }

    state.value.loading = true
    state.value.error = null

    try {
      const response = await getQueryQualificationConfigget({ categoryId })
      state.value.qualificationList = response?.qualificationGroupList || []
    } catch (err) {
      state.value.error = err instanceof Error ? err.message : '加载资质配置失败'
      state.value.qualificationList = []
    } finally {
      state.value.loading = false
    }
  }

  // 更新资质数据
  const updateQualificationData = (qualificationCode: string, data: QualificationItemData) => {
    state.value.qualificationData = {
      ...state.value.qualificationData,
      [qualificationCode]: data
    }
  }

  // 获取资质数据
  const getQualificationData = (qualificationCode: string): QualificationItemData | null => state.value.qualificationData[qualificationCode] || null

  // 验证资质数据完整性
  const validateQualificationData = (qualificationCode: string): boolean => {
    const data = getQualificationData(qualificationCode)
    if (!data) return false

    // 检查是否有图片
    const hasImages = Boolean(data.mediaInfoList && data.mediaInfoList.length > 0)
    // 检查是否有有效期
    const hasValidity = Boolean(
      data.qualificationValidity && (data.qualificationValidity.qualValidityPeriod === 0 || (data.qualificationValidity.startTime && data.qualificationValidity.endTime))
    )

    return hasImages && hasValidity
  }

  // 清空所有数据
  const clearAll = () => {
    state.value.qualificationList = []
    state.value.qualificationData = {}
    state.value.error = null
  }

  // 重置错误状态
  const clearError = () => {
    state.value.error = null
  }

  return {
    // 响应式状态
    loading,
    error,
    qualificationList,
    qualificationData,
    hasQualifications,

    // 方法
    loadQualificationTypes,
    updateQualificationData,
    getQualificationData,
    validateQualificationData,
    clearAll,
    clearError
  }
}
