import type { CategoryPickerValue } from '~/components/CategoryPicker/types'
import type { IQualificationElement } from '@edith/edith_get_query_qualification_config'

/**
 * 补充资质表单数据类型
 */
export interface SupplementaryQualificationsFormData {
  category: CategoryPickerValue | null
  qualificationList: Record<string, any>
}

/**
 * 资质管理配置类型
 */
export interface QualificationManagementConfig {
  autoLoad?: boolean
  validateOnChange?: boolean
  cacheEnabled?: boolean
}

/**
 * 表单验证规则类型
 */
export interface FormValidationRules {
  category: Array<{
    required: boolean
    message: string
    trigger?: string
  }>
  qualificationList: Array<{
    required: boolean
    message: string
    trigger?: string
  }>
}

/**
 * 资质项数据类型
 */
export interface QualificationItemData {
  qualificationCode: string
  qualificationName: string
  qualificationValidity?: {
    qualValidityPeriod: number
    startTime?: number
    endTime?: number | string
  }
  mediaInfoList?: Array<{
    name: string
    uid: string
    size: number
    status: 'uploading' | 'done' | 'error'
    width?: number
    height?: number
    url?: string
  }>
}

/**
 * 资质管理状态类型
 */
export interface QualificationManagementState {
  loading: boolean
  error: string | null
  qualificationList: IQualificationElement[]
  qualificationData: Record<string, QualificationItemData>
}
