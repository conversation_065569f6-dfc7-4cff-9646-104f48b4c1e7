/**
 * 表单验证 Composable
 * 负责统一的表单验证逻辑
 */
import { computed } from 'vue'
import type { FormValidationRules, SupplementaryQualificationsFormData } from './types'

export function useFormValidation() {
  // 表单验证规则
  const formRules = computed<FormValidationRules>(() => ({
    category: [
      {
        required: true,
        message: '请选择主营品类',
        trigger: 'change'
      }
    ],
    qualificationList: [
      {
        required: true,
        message: '请完善资质信息',
        trigger: 'change'
      }
    ]
  }))

  // 验证分类选择
  const validateCategory = (category: any): boolean => {
    return category && Array.isArray(category) && category.length > 0
  }

  // 验证资质数据
  const validateQualificationList = (qualificationData: Record<string, any>): boolean => {
    if (!qualificationData || Object.keys(qualificationData).length === 0) {
      return false
    }

    // 检查每个资质项是否完整
    return Object.values(qualificationData).every((item: any) => {
      if (!item) return false
      
      // 检查是否有图片
      const hasImages = item.mediaInfoList && item.mediaInfoList.length > 0
      
      // 检查是否有有效期
      const hasValidity = item.qualificationValidity && 
        (item.qualificationValidity.qualValidityPeriod === 0 || 
         (item.qualificationValidity.startTime && item.qualificationValidity.endTime))

      return hasImages && hasValidity
    })
  }

  // 验证整个表单
  const validateForm = (formData: SupplementaryQualificationsFormData): {
    valid: boolean
    errors: string[]
  } => {
    const errors: string[] = []

    // 验证分类
    if (!validateCategory(formData.category)) {
      errors.push('请选择主营品类')
    }

    // 验证资质
    if (!validateQualificationList(formData.qualificationList)) {
      errors.push('请完善资质信息')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  // 计算是否可以提交
  const canSubmit = (formData: SupplementaryQualificationsFormData): boolean => {
    return validateForm(formData).valid
  }

  return {
    // 验证规则
    formRules,

    // 验证方法
    validateCategory,
    validateQualificationList,
    validateForm,
    canSubmit
  }
}
