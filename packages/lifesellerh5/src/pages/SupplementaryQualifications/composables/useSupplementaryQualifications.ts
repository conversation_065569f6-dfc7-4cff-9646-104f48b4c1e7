/**
 * 补充资质页面主 Composable
 * 整合各个子 composable，管理页面级别的状态
 */
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import type { CategoryPickerValue } from '~/components/CategoryPicker/types'
import { useCategorySelection } from '../composable'
import { useQualificationManagement } from './useQualificationManagement'
import { useFormValidation } from './useFormValidation'
import type { SupplementaryQualificationsFormData, QualificationItemData } from './types'

export function useSupplementaryQualifications() {
  const router = useRouter()

  // 使用子 composables
  const categorySelection = useCategorySelection()
  const qualificationManagement = useQualificationManagement()
  const formValidation = useFormValidation()

  // 表单数据
  const formData = ref<SupplementaryQualificationsFormData>({
    category: null,
    qualificationList: {}
  })

  // 页面状态
  const isSubmitting = ref(false)
  const submitError = ref<string | null>(null)

  // 计算属性
  const selectedCategoryId = computed(() => categorySelection.selectedCategoryId.value)
  const qualificationList = computed(() => qualificationManagement.qualificationList.value)
  const hasQualifications = computed(() => qualificationManagement.hasQualifications.value)
  const formRules = computed(() => formValidation.formRules.value)
  const canSubmit = computed(() => formValidation.canSubmit(formData.value))

  // 监听分类变化，自动加载对应的资质配置
  watch(
    () => categorySelection.selectedCategoryId.value,
    async (newCategoryId: string) => {
      if (newCategoryId) {
        await qualificationManagement.loadQualificationTypes(newCategoryId)
        // 清空之前的资质数据
        formData.value.qualificationList = {}
      }
    },
    { immediate: false }
  )

  // 处理分类选择确认
  const handleCategoryConfirm = (value: CategoryPickerValue) => {
    categorySelection.updateCategory(value)
    formData.value.category = value
  }

  // 关闭分类选择器
  const closeCategoryPicker = () => {
    // 可以在这里添加关闭时的逻辑
  }

  // 处理资质卡片数据更新
  const handleQualificationCardUpdate = (qualificationCode: string, value: any) => {
    // 更新本地表单数据
    formData.value.qualificationList = {
      ...formData.value.qualificationList,
      [qualificationCode]: value
    }

    // 更新资质管理状态
    if (value && typeof value === 'object') {
      qualificationManagement.updateQualificationData(qualificationCode, value as QualificationItemData)
    }
  }

  // 获取资质项数据
  const getQualificationItemData = (qualificationCode: string) => formData.value.qualificationList[qualificationCode] || null

  // 处理资质加载
  const handleQualificationLoad = (qualificationCode: string) => {
    // 可以在这里添加资质加载时的逻辑
    console.log('Loading qualification:', qualificationCode)
  }

  // 处理上一步
  const handlePrevious = () => {
    router.back()
  }

  // 处理提交
  const handleSubmit = async () => {
    if (isSubmitting.value) return

    // 清除之前的错误
    submitError.value = null

    // 验证表单
    const validation = formValidation.validateForm(formData.value)
    if (!validation.valid) {
      submitError.value = validation.errors.join(', ')
      console.error('表单验证失败:', validation.errors)
      return
    }

    isSubmitting.value = true

    try {
      // 准备提交数据
      const submitData = {
        categoryId: selectedCategoryId.value,
        qualifications: Object.values(formData.value.qualificationList).map(item => ({
          qualificationCode: item.qualificationCode,
          qualificationName: item.qualificationName,
          qualificationValidity: item.qualificationValidity,
          mediaInfoList: item.mediaInfoList
        }))
      }

      console.log('提交数据:', submitData)

      // TODO: 实际的API调用
      // const response = await submitSupplementaryQualifications(submitData)

      // 模拟提交
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 提交成功后的处理
      console.log('提交成功')
      // TODO: 跳转到成功页面或返回上一页
      router.push('/supplementary-qualifications/success')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '提交失败，请重试'
      submitError.value = errorMessage
      console.error('提交失败:', error)
    } finally {
      isSubmitting.value = false
    }
  }

  return {
    // 表单数据
    formData,

    // 分类选择相关
    selectedCategoryId,

    // 资质管理相关
    qualificationList,
    hasQualifications,

    // 表单验证相关
    formRules,
    canSubmit,

    // 方法
    handleCategoryConfirm,
    closeCategoryPicker,
    handleQualificationCardUpdate,
    getQualificationItemData,
    handleQualificationLoad,
    handlePrevious,
    handleSubmit
  }
}
