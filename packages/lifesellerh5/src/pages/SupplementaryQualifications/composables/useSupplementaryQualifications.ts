/**
 * 补充资质页面主 Composable
 * 整合各个子 composable，管理页面级别的状态
 */
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import type { CategoryPickerValue } from '~/components/CategoryPicker/types'
import { useCategorySelection } from '../composable'
import { useQualificationManagement } from './useQualificationManagement'
import { useFormValidation } from './useFormValidation'
import type { SupplementaryQualificationsFormData, QualificationItemData } from './types'

export function useSupplementaryQualifications() {
  const router = useRouter()

  // 使用子 composables
  const categorySelection = useCategorySelection()
  const qualificationManagement = useQualificationManagement()
  const formValidation = useFormValidation()

  // 表单数据
  const formData = ref<SupplementaryQualificationsFormData>({
    category: null,
    qualificationList: {}
  })

  // 页面状态
  const isSubmitting = ref(false)
  const submitError = ref<string | null>(null)

  // 计算属性
  const selectedCategoryId = computed(() => categorySelection.selectedCategoryId.value)
  const qualificationList = computed(() => qualificationManagement.qualificationList.value)
  const hasQualifications = computed(() => qualificationManagement.hasQualifications.value)
  const canSubmit = computed(() => formValidation.canSubmit(formData.value))

  // 注释掉自动监听，改为手动触发
  // watch(
  //   () => categorySelection.selectedCategoryId.value,
  //   async (newCategoryId: string) => {
  //     if (newCategoryId) {
  //       await qualificationManagement.loadQualificationTypes(newCategoryId)
  //       // 清空之前的资质数据
  //       formData.value.qualificationList = {}
  //     }
  //   },
  //   { immediate: false }
  // )

  // 处理分类选择确认
  const handleCategoryConfirm = async (value: CategoryPickerValue) => {
    // 更新分类选择状态
    categorySelection.updateCategory(value)
    formData.value.category = value

    // 获取最后一级分类ID（最具体的分类）
    const categoryId = value && value.length > 0 ? value[value.length - 1] : ''

    if (categoryId) {
      // 清空之前的资质数据
      formData.value.qualificationList = {}

      // 加载对应的资质配置
      await qualificationManagement.loadQualificationTypes(categoryId)
    }
  }

  // 关闭分类选择器
  const closeCategoryPicker = () => {
    // 可以在这里添加关闭时的逻辑
  }

  // 获取资质组数据
  const getQualificationGroupData = (group: any) => {
    const groupData: Record<string, any> = {}

    // 遍历资质组中的所有资质元素
    group.qualificationElements?.forEach((element: any) => {
      const qualificationCode = element.qualificationConfig?.qualificationCode
      if (qualificationCode && formData.value.qualificationList[qualificationCode]) {
        groupData[qualificationCode] = formData.value.qualificationList[qualificationCode]
      }
    })

    return groupData
  }

  // 处理资质卡片数据更新
  const handleQualificationCardUpdate = (_group: any, value: any) => {
    // 更新对应资质组的数据
    if (value && typeof value === 'object') {
      formData.value.qualificationList = {
        ...formData.value.qualificationList,
        ...value
      }

      // 更新资质管理状态
      Object.entries(value).forEach(([code, data]) => {
        qualificationManagement.updateQualificationData(code, data as QualificationItemData)
      })
    }
  }

  // 处理资质加载
  const handleQualificationLoad = (_group: any, _index: number) => {
    // 可以在这里添加资质加载时的逻辑
    // console.log('Loading qualification group:', group, 'index:', index)
  }

  // 处理上一步
  const handlePrevious = () => {
    router.back()
  }

  // 处理提交
  const handleSubmit = async () => {
    if (isSubmitting.value) return

    // 清除之前的错误
    submitError.value = null

    // 验证表单
    const validation = formValidation.validateForm(formData.value)
    if (!validation.valid) {
      submitError.value = validation.errors.join(', ')
      console.error('表单验证失败:', validation.errors)
      return
    }

    isSubmitting.value = true

    try {
      // 准备提交数据
      const submitData = {
        categoryId: selectedCategoryId.value,
        qualifications: Object.values(formData.value.qualificationList).map(item => ({
          qualificationCode: item.qualificationCode,
          qualificationName: item.qualificationName,
          qualificationValidity: item.qualificationValidity,
          mediaInfoList: item.mediaInfoList
        }))
      }

      console.log('提交数据:', submitData)

      // TODO: 实际的API调用
      // const response = await submitSupplementaryQualifications(submitData)

      // 模拟提交
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 提交成功后的处理
      console.log('提交成功')
      // TODO: 跳转到成功页面或返回上一页
      router.push('/supplementary-qualifications/success')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '提交失败，请重试'
      submitError.value = errorMessage
      console.error('提交失败:', error)
    } finally {
      isSubmitting.value = false
    }
  }

  return {
    // 表单数据
    formData,

    // 分类选择相关
    selectedCategoryId,

    // 资质管理相关
    qualificationList,
    hasQualifications,

    // 表单验证相关
    canSubmit,

    // 方法
    handleCategoryConfirm,
    closeCategoryPicker,
    getQualificationGroupData,
    handleQualificationCardUpdate,
    handleQualificationLoad,
    handlePrevious,
    handleSubmit
  }
}
