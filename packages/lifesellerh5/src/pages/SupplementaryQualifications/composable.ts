/**
 * 分类选择管理 Composable
 * 负责主营品类的选择逻辑和状态管理
 */
import { ref, computed } from 'vue'
import type { CategoryPickerValue } from '~/components/CategoryPicker/types'

export function useCategorySelection() {
  // 响应式数据
  const selectedCategory = ref<CategoryPickerValue | null>(null)

  // 计算属性
  const selectedCategoryId = computed(() => {
    if (!selectedCategory.value || selectedCategory.value.length === 0) return ''
    return selectedCategory.value[selectedCategory.value.length - 1]
  })

  const selectedCategoryText = computed(() => {
    if (!selectedCategory.value || selectedCategory.value.length === 0) return ''
    // TODO: 根据选中的类目ID获取实际名称
    return selectedCategory.value.join(' / ')
  })

  const hasSelectedCategory = computed(() =>
    selectedCategory.value && selectedCategory.value.length > 0)

  // 方法
  const updateCategory = (value: CategoryPickerValue | null) => {
    selectedCategory.value = value
  }

  const clearCategory = () => {
    selectedCategory.value = null
  }

  return {
    // 数据
    selectedCategory,

    // 计算属性
    selectedCategoryId,
    selectedCategoryText,
    hasSelectedCategory,

    // 方法
    updateCategory,
    clearCategory
  }
}
