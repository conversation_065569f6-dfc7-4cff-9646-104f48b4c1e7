<template>
  <template v-for="(group, key) in qualificationGroupList" :key="key">
    <component
      :is="group.func === Func.AND ? QualificationAndItem : QualificationOrItem"
      v-if="group.qualificationElements?.length"
      v-model="value"
      :readonly="componentProps?.readonly"
      :elements="group.qualificationElements || []"
    />
  </template>
</template>

<script lang="tsx" setup>
  import { computed } from 'vue'
  import { IQualificationGroupList } from '@edith/edith_get_query_qualification_config'
  import { QualificationItem, Func } from '../../core/type'
  import QualificationOrItem from './QualificationOrItem.vue'
  import QualificationAndItem from './QualificationAndItem.vue'

  const props = withDefaults(
    defineProps<{
      modelValue: Record<string, QualificationItem>
      qualificationGroupList: IQualificationGroupList[]
      componentProps?: {
        categoryId?: string
        disabled?: boolean
        readonly?: boolean
        onLoad?:(loaded: boolean) => void
      }
      name?: string
    }>(),
    {
      name: 'qualificationMap',
      qualificationGroupList: () => []
    }
  )

  const emit = defineEmits(['update:modelValue'])

  const value = computed({
    get: () => props.modelValue,
    set: val => {
      emit('update:modelValue', val)
    }
  })

</script>

<style lang="stylus" scoped>
.qualification-outer
  display flex
  flex-direction column
  gap 20px
  padding-bottom 10px

.gray-card
  background-color #fafafa
  padding 20px
  border-radius 4px
  display flex
  flex-direction column
  gap: 12px
</style>
