<template>
  <component
    :is="qualificationGroup.func === Func.AND ? QualificationAndItem : QualificationOrItem"
    v-if="qualificationGroup.qualificationElements?.length"
    v-model="value"
    :readonly="componentProps?.readonly"
    :elements="qualificationGroup.qualificationElements || []"
  />
</template>

<script lang="tsx" setup>
  import { computed } from 'vue'
  import { IQualificationGroupList } from '@edith/edith_get_query_qualification_config'
  import { QualificationItem, Func } from '../../core/type'
  import QualificationOrItem from './QualificationOrItem.vue'
  import QualificationAndItem from './QualificationAndItem.vue'

  const props = withDefaults(
    defineProps<{
      modelValue: Record<string, QualificationItem>
      qualificationGroup: IQualificationGroupList
      componentProps?: {
        categoryId?: string
        disabled?: boolean
        readonly?: boolean
        onLoad?:(loaded: boolean) => void
      }
      name?: string
    }>(),
    {
      name: 'qualificationMap'
    }
  )

  const emit = defineEmits(['update:modelValue'])

  const value = computed({
    get: () => props.modelValue,
    set: val => {
      emit('update:modelValue', val)
    }
  })

</script>

<style lang="stylus" scoped>
.qualification-outer
  display flex
  flex-direction column
  gap 20px
  padding-bottom 10px

.gray-card
  background-color #fafafa
  padding 20px
  border-radius 4px
  display flex
  flex-direction column
  gap: 12px
</style>
